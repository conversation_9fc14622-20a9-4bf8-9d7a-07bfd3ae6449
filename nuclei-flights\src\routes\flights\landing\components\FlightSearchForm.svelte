<script lang="ts">
  import { base } from "$app/paths";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { createEventDispatcher, onMount } from "svelte";

  import Calendar from "./Calendar.svelte";
  import TravellerClassModal from "./TravellerClassModal.svelte";
  import CitySelector from "./CitySelector.svelte";
  import DateSelector from "./DateSelector.svelte";
  import TravellerSelector from "./TravellerSelector.svelte";
  import SpecialFareSelector from "./SpecialFareSelector.svelte";
  import CompareAndFlyHeader from "./CompareAndFlyHeader.svelte";
  import { dateUtils } from "$lib/flights-commons/utils/date-util";
  import PrimaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";

  export let selectedCities;

  const dispatch = createEventDispatcher();

  let departureDate = "17 Mar";
  let departureDay = "Monday";
  let returnDate = "";
  let isReturnTrip = false;
  let selectedClass = "Economy";
  let travellers = "01";
  let showNonStop = true;
  let selectedSpecialFare = "Student";
  let selectedDate: Date | null = null;
  let showCalendar = false;
  let modalOpen = false;
  let adults = 1;
  let children = 0;
  let infants = 0;

  function swapCities() {
    const tempCity = selectedCities.from;
    selectedCities.from = selectedCities.to;
    selectedCities.to = tempCity;
    selectedCities = { ...selectedCities };
    dispatch("citySwap", selectedCities);
  }

  function toggleReturnTrip() {
    isReturnTrip = !isReturnTrip;
    if (!isReturnTrip) {
      returnDate = "";
    }
  }

  function handleClick(event: any) {
    const type = event.detail;
    console.log("handleClick received type:", type);

    let param;
    if (type === "From") {
      param = "source=true";
      console.log("SOURCE");
    } else if (type === "To") {
      param = "destination=true";
      console.log("DESTINATION");
    } else {
      console.error("Unknown type received:", type);
      param = "destination=true"; // fallback
    }

    console.log(`Navigating to location selection with param: ${param}`);

    NavigatorUtils.navigateTo({
      url: base + `/flights/SearchAirport?${param}`,
      opts: {
        state: {
          selectedCities: selectedCities,
        },
      },
    });
  }

  function handleModalDone(event) {
    const {
      adults: newAdults,
      children: newChildren,
      infants: newInfants,
      selectedClass: newClass,
    } = event.detail;

    adults = newAdults;
    children = newChildren;
    infants = newInfants;
    selectedClass = newClass;

    const totalTravellers = adults + children + infants;
    travellers = totalTravellers.toString().padStart(2, "0");

    console.log("Updated:", {
      clas: selectedClass,
      travellers: travellers,
      breakdown: { adults, children, infants },
    });

    modalOpen = false;
  }

  function handleSearchFlights() {
    dispatch("searchFlights", {
      departure: departureDate,
      passengers: parseInt(travellers),
      clas: selectedClass,
      // isReturnTrip,
      // returnDate,
      // showNonStop,
      // specialFare: selectedSpecialFare
    });
  }

  function openCalendar() {
    console.log("Opening the Calendar...");
    showCalendar = true;
  }

  function updateDepartureDate(date: Date) {
    selectedDate = date;
    departureDate = dateUtils.formatDate(date);
    departureDay = dateUtils.formatDay(date);
  }

  function handleCalendarClose() {
    showCalendar = false;
  }

  function handleCalendarSelect(event: CustomEvent<Date>) {
    const date = event.detail;
    updateDepartureDate(date);
    showCalendar = false;
  }

  function openModal() {
    modalOpen = true;
  }

  function closeModal() {
    modalOpen = false;
  }

  onMount(() => {
    const today = new Date();
    updateDepartureDate(today);
  });
</script>

<div
  class="w-full max-w-lg mx-auto bg-gray-100 p-4 pb-0 rounded-b-lg shadow-md mb-2"
>
  <CompareAndFlyHeader />

  <CitySelector {selectedCities} on:click={handleClick} on:swap={swapCities} />

  <DateSelector
    {departureDate}
    {departureDay}
    {returnDate}
    {isReturnTrip}
    {showCalendar}
    {selectedDate}
    on:openCalendar={openCalendar}
    on:toggleReturn={toggleReturnTrip}
    on:calendarSelect={handleCalendarSelect}
    on:calendarClose={handleCalendarClose}
  >
    <Calendar
      bind:isOpen={showCalendar}
      bind:selectedDate
      position="bottom"
      on:select={handleCalendarSelect}
      on:close={handleCalendarClose}
    />
  </DateSelector>

  <TravellerSelector {selectedClass} {travellers} on:openModal={openModal} />

  <SpecialFareSelector bind:selectedFare={selectedSpecialFare} />

  <!-- Show only non-stop flights checkbox -->
  <div class="flex items-center mb-6">
    <input
      type="checkbox"
      id="nonStopFlights"
      class="form-checkbox h-5 w-5 text-sky-600 rounded"
      bind:checked={showNonStop}
    />
    <label for="nonStopFlights" class="ml-2 text-gray-700 text-sm"
      >Show only non-stop flights</label
    >
  </div>

  <!-- Search Flights Button -->
  <PrimaryButton
    id="Search Flights"
    height="h-14"
    on:submit={handleSearchFlights}
    designClass="w-full bg-green-500 text-white font-bold py-3 rounded-lg text-lg shadow-md hover:bg-green-600 transition duration-300 m-0"
  >
    <div class="whitespace-nowrap font-['Roboto']">Search Flights</div>
  </PrimaryButton>

  <TravellerClassModal
    isOpen={modalOpen}
    initialAdults={adults}
    initialChildren={children}
    initialInfants={infants}
    initialClass={selectedClass}
    on:close={closeModal}
    on:done={handleModalDone}
  />
</div>
