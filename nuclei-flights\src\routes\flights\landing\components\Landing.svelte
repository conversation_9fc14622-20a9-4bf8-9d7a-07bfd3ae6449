<script lang="ts">
  // Import required modules
  import { base } from "$app/paths";
  import { ListCouponCta } from "@CDNA-Technologies/svelte-vitals/cart/coupons";
  import { LandingRewardCta } from "@CDNA-Technologies/svelte-vitals/cart/rewards";
  import { LandingWalletCta } from "@CDNA-Technologies/svelte-vitals/cart/wallet";
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
  import ThreeDotMenu from "@CDNA-Technologies/svelte-vitals/components/three-dot-menu";
  import {
    ErrorHandling,
    lceStore,
    setContentLce,
    setLoadingLce,
  } from "@CDNA-Technologies/svelte-vitals/error-handling";
  import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { onMount } from "svelte";

  // Fixed imports based on your file structure
  import FlightSearchForm from "./FlightSearchForm.svelte";
  import { flightStore } from "$lib/flights-commons/stores/flight-store.ts";
  import { cityUtils } from "$lib/flights-commons/utils/city-util.ts";

  //Session storage to store user selected cities
  let selectedCities = cityUtils.getInitialCities();

  function handleNavigationState() {
    // Get the navigation state
    const state = history.state;
    NucleiLogger.logInfo("Navigation state:", "LANDING", state);

    if (state && state.selectionType && state.city) {
      NucleiLogger.logDebug("Processing navigation state:", "LANDING", state);

      const { selectionType, city } = state;

      // Update the appropriate city based on selection type
      if (selectionType === "Source") {
        selectedCities.from = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
        NucleiLogger.logInfo(
          "Updated source city:",
          "LANDING",
          selectedCities.from
        );
      } else if (selectionType === "Destination") {
        selectedCities.to = {
          city: city.city,
          code: city.code,
          airport: city.airport,
        };
        NucleiLogger.logInfo(
          "Updated destination city:",
          "LANDING",
          selectedCities.to
        );
      }

      // Trigger reactivity by reassigning the object
      selectedCities = { ...selectedCities };
      cityUtils.saveSelectedCities(selectedCities);

      // Clear the state to prevent reprocessing on refresh
      history.replaceState(null, "", window.location.href);
    }
  }

  function handleCitySwap(event: any) {
    selectedCities = event.detail;
    cityUtils.saveSelectedCities(selectedCities);
  }

  function handleSearchFlights(event: any) {
    const searchData = event.detail;

    // Build query parameters
    const params = new URLSearchParams({
      fromCity: selectedCities.from.city,
      fromCode: selectedCities.from.code,
      fromAirport: selectedCities.from.airport,
      toCity: selectedCities.to.city,
      toCode: selectedCities.to.code,
      toAirport: selectedCities.to.airport,
      departure: searchData.departure,
      passengers: searchData.passengers.toString(),
      class: searchData.clas,
    });

    NavigatorUtils.navigateTo({
      url: `/flights-base/dev/flights/listing?${params.toString()}`,
    });
  }

  onMount(async () => {
    NucleiLogger.logInfo("LANDING", "Landing screen mounted");

    setLoadingLce();
    handleNavigationState();
    await fetchScreenData();

    if (typeof window !== "undefined") {
      window.addEventListener("popstate", handleNavigationState);
    }
  });

  const fetchScreenData = async () => {
    setContentLce();
  };

  function handleRetry() {
    setLoadingLce();
    fetchScreenData();
  }
</script>

<AppBar title="Flights">
  <div slot="action">
    <div class="flex flex-row items-center gap-2">
      <LandingWalletCta />
      <ListCouponCta />
      <LandingRewardCta />
      <div class="dropdown dropdown-end"><ThreeDotMenu /></div>
    </div>
  </div>
</AppBar>

{#if $lceStore.isLoading}
  <div class="h-screen flex flex-col justify-center">
    <PrimaryLoader />
  </div>
{:else if $lceStore.hasError && $lceStore.errorDetails != null}
  <ErrorHandling
    errorHandling={$lceStore.errorDetails}
    on:submit={handleRetry}
  />
{:else if $lceStore.hasContent}
  <div class="flex-1 overflow-y-auto">
    <FlightSearchForm
      {selectedCities}
      on:citySwap={handleCitySwap}
      on:searchFlights={handleSearchFlights}
    />
  </div>
{/if}
