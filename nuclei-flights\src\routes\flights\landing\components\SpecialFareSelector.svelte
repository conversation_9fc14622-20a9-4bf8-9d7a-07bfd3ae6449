<script lang="ts">
  export let selectedFare = "Student";
import SecondaryButton from "@CDNA-Technologies/svelte-vitals/components/primary-button";
  const fares = [
    { id: "Student", label: "Student" },
    { id: "Senior Citizen", label: "Senior Citizen" },
    { id: "Armed Forces", label: "Armed Forces" },
  ];

  function selectFare(fare: string) {
    selectedFare = fare;
  }
</script>

<!-- Special Fares (Optional) -->
<div class="mb-4">
  <div class="select-none text-gray-500 text-sm mb-2">
    Special Fares (Optional)
  </div>
  <div class="select-none flex flex-wrap gap-2">
    {#each fares as fare}
      <button
        class="px-3 py-1 rounded-full text-sm font-medium border transition-colors duration-200
					{selectedFare === fare.id
          ? 'bg-blue-100 text-blue-700 border-blue-300'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}"
        on:click={() => selectFare(fare.id)}
      >
        {fare.label}
      </button>
    {/each}
  </div>
</div>
