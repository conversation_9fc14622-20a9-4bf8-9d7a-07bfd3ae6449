<script lang="ts">
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  // props from parent component
  export let heading: string;
  export let searchQuery: string;
  export let isSearching: boolean;
  export let airportsToShow: any[];
  export let recentSearches: any[];
  export let popularCities: any[];
  export let isLoadingPopular: boolean;
  export let handleSearchInput: (event: Event) => void;
  export let handleCityClick: (airport: any) => void;
  export let selectedCities: any = null;
  export let isSourceSelection: boolean = false;
  export let showDuplicateError: boolean = false;
  export let duplicateErrorMessage: string = "";
</script>

<div class="select-none bg-white min-h-screen">
  <AppBar title={heading} />

  <div class="px-5 py-4 border-b border-gray-200">
    <div class="relative">
      <input
        type="text"
        placeholder="Enter City/Airport Name"
        value={searchQuery}
        on:input={handleSearchInput}
        class="select-none w-full pl-4 pr-11 py-3 bg-gray-50 border border-gray-300 rounded-lg text-base placeholder-gray-400 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200"
      />
      <svg
        class="absolute right-3.5 top-1/2 transform -translate-y-1/2 text-gray-500"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <circle cx="11" cy="11" r="8" />
        <path d="M21 21l-4.35-4.35" />
      </svg>

      <!-- Loading indicator -->
      {#if isSearching}
        <div class="absolute right-10 top-1/2 transform -translate-y-1/2">
          <div
            class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"
          />
        </div>
      {/if}
    </div>
  </div>

  <!-- Error message below search bar -->
  {#if showDuplicateError}
    <div class="px-5 py-2">
      <div class="bg-red-50 border border-red-200 rounded-lg p-3">
        <div class="flex items-center">
          <svg
            class="w-5 h-5 text-red-600 mr-2"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-sm text-red-800 font-medium">
            {duplicateErrorMessage}
          </span>
        </div>
      </div>
    </div>
  {/if}

  <!-- Conditional rendering for search results or default lists -->
  {#if searchQuery}
    <div class="max-h-96 overflow-y-auto border-b border-gray-200">
      {#if isSearching}
        <div class="flex justify-center items-center py-8">
          <div
            class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"
          />
          <span class="select-none">Searching</span>
          <span class="flex space-x-1">
            <span class="animate-bounce">.</span>
            <span class="animate-bounce [animation-delay:0.2s]">.</span>
            <span class="animate-bounce [animation-delay:0.4s]">.</span>
          </span>
        </div>
      {:else if searchQuery.length <= 2}
        <div class="flex justify-center items-center py-8">
          <span class="select-none text-gray-500"
            >Enter More to Find Airports</span
          >
        </div>
      {:else if airportsToShow.length > 0}
        {#each airportsToShow as airport}
          <button
            class="w-full flex items-center py-3 px-5 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
            on:click={() => handleCityClick(airport)}
          >
            <div
              class="w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
            >
              {airport.code}
            </div>
            <div class="flex-1 min-w-0 text-left">
              <div
                class="select-none text-base font-medium text-gray-900 mb-0.5"
              >
                {airport.name}
              </div>
              <div class="select-none text-xs text-gray-500 leading-snug">
                {airport.airport}
              </div>
            </div>
          </button>
        {/each}
      {:else if searchQuery.length > 2}
        <div class="flex justify-center items-center py-8">
          <span class="select-none text-gray-500">No airports found</span>
        </div>
      {/if}
    </div>
  {:else}
    <!-- Recent Searches Section -->
    {#if recentSearches.length > 0}
      <div class="px-5">
        <div class="flex items-center py-4 pb-3 border-b border-gray-100">
          <svg
            class="text-gray-500 mr-2"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <circle cx="12" cy="12" r="10" />
            <polyline points="12,6 12,12 16,14" />
          </svg>
          <span
            class="select-none text-sm font-medium text-gray-500 uppercase tracking-wider"
          >
            Recent Searches
          </span>
        </div>
        <div class="pb-2">
          {#each recentSearches as airport}
            <button
              class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
              on:click={() => handleCityClick(airport)}
            >
              <div
                class="select-none w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
              >
                {airport.code}
              </div>
              <div class="flex-1 min-w-0 text-left">
                <div
                  class="select-none text-base font-medium text-gray-900 mb-0.5"
                >
                  {airport.name}
                </div>
                <div class="select-none text-xs text-gray-500 leading-snug">
                  {airport.airport}
                </div>
              </div>
            </button>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Popular Cities Section -->
    <div class="px-5">
      <div class="flex items-center py-4 pb-3 border-b border-gray-100">
        <svg
          class="text-gray-500 mr-2"
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
          <circle cx="12" cy="10" r="3" />
        </svg>
        <span
          class="select-none text-sm font-medium text-gray-500 uppercase tracking-wider"
        >
          Popular Cities
        </span>
      </div>

      {#if isLoadingPopular}
        <div class="flex justify-center items-center py-8">
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
          />
          <span class="select-none ml-2 text-gray-500"
            >Loading popular cities...</span
          >
        </div>
      {:else}
        <div class="pb-2">
          {#each popularCities as airport}
            <button
              class="w-full flex items-center py-3 cursor-pointer transition-colors duration-200 border-b border-gray-100 hover:bg-slate-50 last:border-b-0"
              on:click={() => handleCityClick(airport)}
            >
              <div
                class="select-none w-[34px] h-[34px] bg-gray-100 rounded flex items-center justify-center text-xs font-semibold text-gray-700 mr-3 flex-shrink-0"
              >
                {airport.code}
              </div>
              <div class="flex-1 min-w-0 text-left">
                <div
                  class="select-none text-base font-medium text-gray-900 mb-0.5"
                >
                  {airport.name}
                </div>
                <div class="select-none text-xs text-gray-500 leading-snug">
                  {airport.airport}
                </div>
              </div>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>
