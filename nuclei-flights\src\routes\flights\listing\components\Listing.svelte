<script lang="ts">
  import AppBar from "@CDNA-Technologies/svelte-vitals/components/appbar";
  import PrimaryLoader from "@CDNA-Technologies/svelte-vitals/components/primary-loader";
  import {
    ErrorHandling,
    lceStore,
    setContentLce,
    setLoadingLce,
    setErrorLce,
  } from "@CDNA-Technologies/svelte-vitals/error-handling";
  import { NucleiLogger } from "@CDNA-Technologies/svelte-vitals/logger";
  import { page } from "$app/stores";
  import { onMount } from "svelte";
  import dayjs from "dayjs";
  import { base } from "$app/paths";
  import { NavigatorUtils } from "@CDNA-Technologies/svelte-vitals/navigator";
  import { fetchFlights } from "$lib/flights-commons/utils/flight-search-api.util.js";
  import type { FlightSearchParams } from "$lib/flights-commons/messages/flight-search-params.msg.ts";
  import type { TransformedFlight } from "$lib/flights-commons/messages/flight-search-params.msg.js";
  // Import components
  import FilterControls from "./FilterControls.svelte";
  import FlightList from "./FlightList.svelte";

  // Get data from query parameters instead of state
  $: fromCity = {
    city: $page.url.searchParams.get("fromCity") || "New Delhi",
    code: $page.url.searchParams.get("fromCode") || "DEL",
    airport:
      $page.url.searchParams.get("fromAirport") ||
      "Indira Gandhi International Airport",
  };

  $: toCity = {
    city: $page.url.searchParams.get("toCity") || "Mumbai",
    code: $page.url.searchParams.get("toCode") || "BOM",
    airport:
      $page.url.searchParams.get("toAirport") ||
      "Chhatrapati Shivaji International Airport",
  };

  $: travelDate = $page.url.searchParams.get("departure") || "22 Dec";
  $: travellers = parseInt($page.url.searchParams.get("passengers") || "1");
  $: classType = $page.url.searchParams.get("class") || "Economy";

  NucleiLogger.logInfo("LISTING", "data from query params: ", {
    fromCity,
    toCity,
    travelDate,
    travellers,
    classType,
  });

  const source = fromCity.city;
  const dest = toCity.city;
  const sourceCode = fromCity.code;
  const destCode = toCity.code;
  let appBarTitle = `(${sourceCode}) ${source} → (${destCode}) ${dest}`;

  // Ensure we always use a valid future date
  const depart = (() => {
    const providedDate = travelDate;
    if (providedDate) {
      const parsedDate = dayjs(providedDate);
      // Check if the date is valid and in the future
      if (parsedDate.isValid() && parsedDate.isAfter(dayjs(), "day")) {
        return providedDate;
      }
    }
    // Fallback to tomorrow's date if no valid future date is provided
    return dayjs().add(1, "day").format("YYYY-MM-DD");
  })();
  const pass = travellers;
  const clas = classType;

  let appBarSubtitle = `${travelDate} | ${pass} Traveller${
    pass > 1 ? "s" : ""
  } | ${clas}`;

  // Initialize with empty flights array - will be populated by API
  let flights: TransformedFlight[] = [];

  // Date options for the date selector (Future Use keep this in a same file;)
  let dateOptions = [
    { day: "Tue, 27 Jun", price: 4807 },
    { day: "Wed, 28 Jun", price: 5203 },
    { day: "Thu, 29 Jun", price: 6100 },
    { day: "Fri, 30 Ju", price: 4500 },
  ];
  //Future Use
  let selectedDateIndex = 1; // Wed is selected by default
  let sortBy = "cheapest";
  let showRefundableOnly = true;

  // Main function to fetch flights using the API service
  const fetchScreenData = async () => {
    try {
      // Prepare search parameters
      const searchParams: FlightSearchParams = {
        source: {
          city: source,
          iataCode: sourceCode,
          countryCode: "IN",
        },
        destination: {
          city: dest,
          iataCode: destCode,
          countryCode: "IN",
          fareType: "regular",
          partnerCountry: "IN",
        },
        departureDate: depart,
        passengers: {
          adults: pass,
          children: 0,
          infants: 0,
        },
        travellerClass: {
          displayName: clas as
            | "Economy Class"
            | "Business Class"
            | "Premium Class",
          apiKey:
            clas === "Business Class"
              ? "BUSINESS"
              : clas === "Premium Class"
              ? "PREMIUM"
              : "ECONOMY",
        },
      };
      // Call the API service
      const result = await fetchFlights(searchParams);

      if (result.hasError) {
        throw new Error(result.error);
      }

      // Update flights array with the fetched data
      flights = result.flights;
      NucleiLogger.logInfo("LISTING", "Fetched flights:", flights);
      setContentLce();
    } catch (error) {
      NucleiLogger.logException(`Failed to fetch flights: ${error}`, "Flights");
      setErrorLce({
        title: "Failed to load flights",
        message: "Unable to fetch flight data. Please try again.",
        actionText: "Retry",
      });
    }
  };

  onMount(async () => {
    NucleiLogger.logInfo("Flights", "Listing screen mounted");
    setLoadingLce();
    await fetchScreenData();
  });

  function handleRetry() {
    setLoadingLce();
    fetchScreenData();
  }

  function handleDateSelect(event: any) {
    selectedDateIndex = event.detail.index;
  }

  function handleSortFilter() {
    // Handle sort & filter functionality
    console.log("Sort & Filter clicked");
  }

  function toggleRefundable() {
    showRefundableOnly = !showRefundableOnly;
  }

  function handleFlightSelect(event: any) {
    const flight = event.detail.flight;
    console.log("Selected flight:", flight);
    // Navigate to flight details or booking
  }

  function handleBackButton() {
    NavigatorUtils.navigateTo({
      url: base + "/flights/landing",
    });
  }
</script>

<div class="select-none h-screen flex flex-col bg-gray-50">
  <AppBar onBackButtonClick={handleBackButton}>
    <div slot="title" class="flex flex-col leading-tight">
      <p class="pl-5 text-base font-bold">{appBarTitle}</p>
      <p class="pl-5 text-sm text-black-400">{appBarSubtitle}</p>
    </div>
  </AppBar>

  {#if $lceStore.isLoading}
    <div class="h-screen flex flex-col justify-center">
      <PrimaryLoader />
    </div>
  {:else if $lceStore.hasError && $lceStore.errorDetails != null}
    <ErrorHandling
      errorHandling={$lceStore.errorDetails}
      on:submit={handleRetry}
    />
  {:else if $lceStore.hasContent}
    <div class="flex-1 flex flex-col">
      <FilterControls
        {dateOptions}
        {selectedDateIndex}
        {showRefundableOnly}
        on:dateSelect={handleDateSelect}
        on:sortFilter={handleSortFilter}
        on:toggleRefundable={toggleRefundable}
      />

      <FlightList {flights} on:flightSelect={handleFlightSelect} />
    </div>
  {/if}
</div>
