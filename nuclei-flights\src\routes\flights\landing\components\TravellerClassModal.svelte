<script>
	import { createEventDispatcher } from 'svelte';
import PrimaryButton from '@CDNA-Technologies/svelte-vitals/components/primary-button';
	export let isOpen = false;

	const dispatch = createEventDispatcher();

	export let initialAdults = 1;
	export let initialChildren = 0;
	export let initialInfants = 0;
	export let initialClass = 'Economy Class';

	let adults = initialAdults;
	let children = initialChildren;
	let infants = initialInfants;
	let selectedClass = initialClass;

	function closeModal() {
		dispatch('close');
	}

	function incrementAdults() {
		if (adults < 9 && getTotalPassengers() < 10) adults++;
	}

	function decrementAdults() {
		if (adults > 1) adults--;
	}

	function incrementChildren() {
		if(getTotalPassengers() < 10 && adults + children<=9)children++;
	}

	function decrementChildren() {
		if (children > 0) children--;
	}

	function incrementInfants() {
		if(getTotalPassengers() < 10 && infants < adults && infants < 1)infants++;
	}

	function decrementInfants() {
		if (infants > 0) infants--;
	}

	function getTotalPassengers() {
		return adults + children + infants;
	}

	function selectClass(className) {
		selectedClass = className;
	}

	function handleDone() {
		dispatch('done', {
			adults,
			children,
			infants,
			selectedClass
		});
		closeModal();
	}
</script>

{#if isOpen}
	<!-- Backdrop -->
	<div class="fixed inset-0 bg-black bg-opacity-50 z-40" on:click={closeModal} />

	<!-- Modal -->
	<div class="fixed inset-x-0 bottom-0 bg-white rounded-t-2xl z-50 animate-slide-up">
		<!-- Close Button -->
		<div class="flex justify-end p-4">
			<button on:click={closeModal} class="select-none text-blue-500 text-2xl hover:text-blue-600">
				✕
			</button>
		</div>

		<div class="px-6 pb-8">
			<!-- Select Traveller Section -->
			<div class="mb-8">
				<h2 class="select-none text-xl font-bold text-gray-900 mb-6">Select Traveller(s)</h2>

				<!-- Adults -->
				<div class="flex justify-between items-center mb-6">
					<div>
						<div class="select-none font-semibold text-gray-900">Adults</div>
						<div class="select-none text-sm text-gray-500">12 years and above</div>
					</div>
					<div class="flex items-center">
						<button
							on:click={decrementAdults}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							-
						</button>
						<span class="select-none mx-4 font-semibold">{adults}</span>
						<button
							on:click={incrementAdults}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							+
						</button>
					</div>
				</div>

				<!-- Children -->
				<div class="flex justify-between items-center mb-6">
					<div>
						<div class="select-none font-semibold text-gray-900">Children</div>
						<div class="select-none text-sm text-gray-500">2 to 12 years</div>
					</div>
					<div class="flex items-center">
						<button
							on:click={decrementChildren}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							−
						</button>
						<span class="mx-4 font-semibold">{children}</span>
						<button
							on:click={incrementChildren}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							+
						</button>
					</div>
				</div>

				<!-- Infants -->
				<div class="flex justify-between items-center mb-6">
					<div>
						<div class="select-none font-semibold text-gray-900">Infants</div>
						<div class="select-none text-sm text-gray-500">Less than 2 years</div>
					</div>
					<div class="flex items-center">
						<button
							on:click={decrementInfants}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							-
						</button>
						<span class="select-none mx-4 font-semibold">{infants}</span>
						<button
							on:click={incrementInfants}
							class="select-none w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50"
						>
							+
						</button>
					</div>
				</div>
			</div>

			<!-- Select Class Section -->
			<div class="mb-8">
				<h2 class="select-none text-xl font-bold text-gray-900 mb-6">Select Class</h2>

				<div class="space-y-4">
					<!-- Economy Class -->
					<div
						on:click={() => selectClass('Economy Class')}
						class="select-none flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
					>
						<div
							class={`w-5 h-5 rounded-full border-2 mr-4 ${
								selectedClass === 'Economy Class'
									? 'border-blue-500 bg-blue-500 '
									: 'border-gray-300'
							}`}
						>
							{#if selectedClass === 'Economy Class'}
								<div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
							{/if}
						</div>
						<span class="select-none text-gray-900">Economy Class</span>
					</div>

					<!-- Business Class -->
					<div
						on:click={() => selectClass('Business Class')}
						class="flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
					>
						<div
							class={`w-5 h-5 rounded-full border-2 mr-4 ${
								selectedClass === 'Business Class'
									? 'border-blue-500 bg-blue-500'
									: 'border-gray-300'
							}`}
						>
							{#if selectedClass === 'Business Class'}
								<div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
							{/if}
						</div>
						<span class="select-none text-gray-900">Business Class</span>
					</div>

					<!-- Premium Economy Class -->
					<div
						on:click={() => selectClass('Premium Economy Class')}
						class="flex items-center cursor-pointer hover:bg-gray-50 p-2 -mx-2 rounded"
					>
						<div
							class={`w-5 h-5 rounded-full border-2 mr-4 ${
								selectedClass === 'Premium Economy Class'
									? 'border-blue-500 bg-blue-500'
									: 'border-gray-300'
							}`}
						>
							{#if selectedClass === 'Premium Economy Class'}
								<div class="w-2 h-2 bg-blue-500 rounded-full m-auto mt-0.5" />
							{/if}
						</div>
						<span class="select-none text-gray-900">Premium Economy Class</span>
					</div>
				</div>
			</div>

			<!-- Done Button -->
			<button
				on:click={handleDone}
				class="select-none w-full bg-green-500 hover:bg-green-600 text-white py-4 rounded-lg font-semibold text-lg transition-colors"
			>
				Done
			</button>
		</div>
	</div>
{/if}

<style>
	@keyframes slide-up {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	.animate-slide-up {
		animation: slide-up 0.3s ease-out;
	}
</style>
